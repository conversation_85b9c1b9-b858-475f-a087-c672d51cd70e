#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Detection Agent for Thunderbird Dataset with BootstrapFinetune
Uses Ollama with qwen3:30b model and BootstrapFinetune for creating a few-shot model.
Counts total lines and randomly selects lines for training and testing.
"""

import dspy
import pandas as pd
import re
from typing import List, Tuple, Dict, Set
import random
from datetime import datetime
import os
from dspy.teleprompt import BootstrapFinetune
from dspy.clients.lm_local import LocalProvider


class LogAnomalySignature(dspy.Signature):
    """Classify a log entry as normal or anomalous based on its content."""
    
    log_entry = dspy.InputField(desc="The log entry content to analyze")
    classification = dspy.OutputField(desc="Classification: 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification")


class LogAnomalyDetector(dspy.Module):
    """DSPy module for detecting anomalies in log entries."""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogAnomalySignature)
    
    def forward(self, log_entry: str):
        """Forward pass through the module."""
        result = self.classify(log_entry=log_entry)
        return result


class ThunderbirdRandomSamplingProcessor:
    """Processes the Thunderbird dataset with random line sampling."""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.total_lines = 0
        self.data = []
    
    def count_total_lines(self) -> int:
        """Count total lines in the dataset file."""
        print(f"Counting total lines in {self.dataset_path}...")
        
        line_count = 0
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Counted {line_num} lines...")
                line_count = line_num
        
        self.total_lines = line_count
        print(f"Total lines in dataset: {self.total_lines:,}")
        return self.total_lines
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from Thunderbird format."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        node = parts[3]
        message = parts[4]
        
        # Label: "-" means normal (non-alert), anything else is anomalous (alert)
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip()
        }
    
    def load_stratified_random_sample(self, num_samples: int = 1000, balance_ratio: float = 0.3) -> List[Dict]:
        """Load stratified random samples to maintain class balance."""
        print(f"Loading {num_samples} stratified random samples (target {balance_ratio:.1%} anomalous)...")
        
        target_anomalous = int(num_samples * balance_ratio)
        target_normal = num_samples - target_anomalous
        
        # First pass: collect all line numbers by class
        normal_lines = []
        anomalous_lines = []
        
        print("First pass: identifying line numbers by class...")
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Scanned {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is not None:
                    if parsed['is_anomalous']:
                        anomalous_lines.append(line_num)
                    else:
                        normal_lines.append(line_num)
        
        print(f"Found {len(normal_lines):,} normal lines and {len(anomalous_lines):,} anomalous lines")
        
        # Sample random line numbers from each class
        selected_normal_lines = set(random.sample(normal_lines, min(target_normal, len(normal_lines))))
        selected_anomalous_lines = set(random.sample(anomalous_lines, min(target_anomalous, len(anomalous_lines))))
        
        all_selected_lines = selected_normal_lines | selected_anomalous_lines
        
        print(f"Selected {len(selected_normal_lines)} normal and {len(selected_anomalous_lines)} anomalous line numbers")
        
        # Second pass: collect the actual data
        samples = []
        print("Second pass: collecting selected lines...")
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines, collected {len(samples)} samples...")
                
                if line_num in all_selected_lines:
                    parsed = self.parse_log_line(line)
                    if parsed is not None:
                        samples.append(parsed)
                
                # Early exit if we've collected all samples
                if len(samples) >= len(all_selected_lines):
                    break
        
        # Shuffle the collected samples
        random.shuffle(samples)
        
        # Print final statistics
        normal_count = sum(1 for item in samples if not item['is_anomalous'])
        anomalous_count = len(samples) - normal_count
        
        print(f"Final stratified sample: {len(samples)} total")
        print(f"  Normal: {normal_count} ({normal_count/len(samples)*100:.1f}%)")
        print(f"  Anomalous: {anomalous_count} ({anomalous_count/len(samples)*100:.1f}%)")
        
        return samples
    
    def create_dspy_examples(self, data: List[Dict], for_training: bool = True) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']
            
            # For BootstrapFinetune, the training set needs full labels.
            # The test set needs inputs and a ground truth label for evaluation.
            if for_training:
                example = dspy.Example(
                    log_entry=clean_entry,
                    classification=item['label'],
                    reasoning=f"This log entry is {item['label']} based on its content and patterns."
                ).with_inputs('log_entry')
            else: # For testing
                example = dspy.Example(
                    log_entry=clean_entry
                ).with_inputs('log_entry')
                # Store the ground truth separately for evaluation
                example.ground_truth_classification = item['label']
            
            examples.append(example)
        
        return examples


def setup_ollama_client(base_url: str = "http://192.168.0.105:11434"):
    """Setup Ollama client with qwen3:30b model."""
    print(f"Setting up Ollama client at {base_url}")
    # Note: For non-finetunable models like this, BootstrapFinetune acts as BootstrapFewShot.
    lm = dspy.LM("openai/gpt-4o-mini")
    dspy.configure(lm=lm)
    return lm


def create_evaluation_metric():
    """Create evaluation metric for the optimizer."""
    def evaluate_accuracy(example, pred, trace=None):
        """Evaluate prediction accuracy."""
        try:
            # For BootstrapFinetune, the example object passed during compilation has full labels.
            # During evaluation on the test set, we use our custom `ground_truth_classification`.
            if hasattr(example, 'ground_truth_classification'):
                actual_class = example.ground_truth_classification.lower().strip()
            else: # During the bootstrapping/training phase
                actual_class = example.classification.lower().strip()
                
            predicted_class = pred.classification.lower().strip()
            return predicted_class == actual_class
        except:
            return False

    return evaluate_accuracy

def train_and_evaluate_bootstrap():
    """Main training and evaluation function with BootstrapFinetune."""
    print("Starting Thunderbird Log Anomaly Detection with BootstrapFinetune...")

    # 1. Capture the returned language model object
    lm = setup_ollama_client()

    # Load and process data with random sampling
    processor = ThunderbirdRandomSamplingProcessor("dataset/Thunderbird/Thunderbird_10M.log")

    # Load stratified random sample (maintains class balance)
    # Use large sample for GPU training
    sample_data = processor.load_stratified_random_sample(num_samples=10000, balance_ratio=0.2)

    # Split data into train and test sets (80/20 split for large dataset)
    train_size = int(0.8 * len(sample_data))
    train_data = sample_data[:train_size]
    test_data = sample_data[train_size:]

    # Create examples
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    test_examples = processor.create_dspy_examples(test_data, for_training=False)

    print(f"Dataset split: {len(train_examples)} train, {len(test_examples)} test")

    # Initialize the model (student)
    detector = LogAnomalyDetector()

    student_lm_name = "Qwen/Qwen2.5-0.5B-Instruct"  # Smallest Qwen model for fine-tuning
    # Use CPU for fine-tuning to avoid GPU memory issues
    student_lm = dspy.LM(model=f"openai/local:{student_lm_name}", provider=LocalProvider(), max_tokens=2000)
    teacher_lm = dspy.LM('openai/gpt-4.1-mini', api_key="********************************************************************************************************************************************************************",max_tokens=3000)

    student_classify = detector.deepcopy()
    student_classify.set_lm(student_lm)

    teacher_classify = detector.deepcopy()
    teacher_classify.set_lm(teacher_lm)

    dspy.settings.experimental = True  # fine-tuning is an experimental feature, so we set a flag to enable it


    # Create evaluation metric
    evaluate_accuracy = create_evaluation_metric()

    train_kwargs = {
            "use_peft": False,  # Enable LoRA
            "per_device_train_batch_size": 1,
            "gradient_accumulation_steps": 32,
            "max_seq_length": 4096,
            "bf16": True,
            "gradient_checkpointing": True,
            "num_train_epochs": 3,  # Reduce epochs
    }

    # Use BootstrapFinetune for large dataset training
    optimizer = dspy.BootstrapFinetune(metric=evaluate_accuracy, num_threads=16, train_kwargs=train_kwargs)
    finetuned_detector = optimizer.compile(student_classify, teacher=teacher_classify, trainset=train_examples)
    finetuned_detector.save("gpt-qwen3-1b.json")
    finetuned_detector.get_lm().launch()


    # --- The rest of the function remains the same ---
    
    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0

    # For F1-score and recall calculation
    true_positives = 0
    false_positives = 0
    false_negatives = 0
    true_negatives = 0

    for example in test_examples:
        try:
            prediction = finetuned_detector(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()

            if predicted_class == actual_class:
                correct += 1

            # Calculate confusion matrix components
            if actual_class == 'anomalous' and predicted_class == 'anomalous':
                true_positives += 1
            elif actual_class == 'normal' and predicted_class == 'anomalous':
                false_positives += 1
            elif actual_class == 'anomalous' and predicted_class == 'normal':
                false_negatives += 1
            elif actual_class == 'normal' and predicted_class == 'normal':
                true_negatives += 1

            total += 1
            if total <= 5:
                print(f"\nExample {total}:")
                print(f"Log: {example.log_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")

        except Exception as e:
            print(f"Error processing example: {e}")
            continue

    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    print(f"\nFinal Results (BootstrapFinetune):")
    print(f"Samples used: {len(sample_data):,}")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1-Score: {f1_score:.3f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives (TP): {true_positives}")
    print(f"False Positives (FP): {false_positives}")
    print(f"False Negatives (FN): {false_negatives}")
    print(f"True Negatives (TN): {true_negatives}")

    return finetuned_detector


if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)

    # Train and evaluate the model using BootstrapFinetune
    trained_model = train_and_evaluate_bootstrap()

    # Save the compiled model (with the few-shot examples included in its prompts)
    model_filename = "thunderbird_bootstrap_model.json"
    trained_model.save(model_filename)
    print(f"\nThunderbird BootstrapFinetune training completed!")
    print(f"Model saved to {model_filename}")
